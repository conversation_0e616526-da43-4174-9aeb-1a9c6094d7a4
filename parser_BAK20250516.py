import pandas as pd

def parse_log(file_path):
    try:
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            lines = [line.strip() for line in f if not line.startswith("#")]

        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            fields_line = next(line for line in f if line.startswith("#Fields:"))
            headers = fields_line.replace("#Fields: ", "").strip().split(",")

        data = [line.split(",", len(headers) - 1) for line in lines]
        df = pd.DataFrame(data, columns=headers)

        # ✅ 將時間欄位解析為無時區 (tz-naive)
        if 'date-time' in df.columns:
            df['date-time'] = pd.to_datetime(df['date-time'], errors='coerce', utc=True)
            df['date-time'] = df['date-time'].dt.tz_localize(None)

        return df

    except Exception as e:
        print(f"❌ 無法解析 LOG 檔：{file_path}\n錯誤：{e}")
        return pd.DataFrame()


def filter_logs(df, keyword=None, start_date=None, end_date=None):
    if keyword:
        # 對所有欄位執行關鍵字模糊查詢
        mask = df.apply(lambda row: row.astype(str).str.contains(keyword, case=False, na=False).any(), axis=1)
        df = df[mask]

    if start_date:
        df = df[df['date-time'] >= pd.to_datetime(start_date)]

    if end_date:
        df = df[df['date-time'] <= pd.to_datetime(end_date)]

    return df
