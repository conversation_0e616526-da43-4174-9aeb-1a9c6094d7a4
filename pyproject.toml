[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "exchange-log-analyzer"
version = "1.0.9"
description = "A tool for analyzing Microsoft Exchange message tracking logs"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
maintainers = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: System Administrators",
    "Intended Audience :: Information Technology",
    "Topic :: System :: Systems Administration",
    "Topic :: Communications :: Email",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Operating System :: OS Independent",
    "Environment :: X11 Applications :: Qt",
]
keywords = ["exchange", "email", "log", "analysis", "tracking", "microsoft"]
requires-python = ">=3.7"
dependencies = [
    "pandas>=1.3.0",
    "PyQt5>=5.15.0",
    "pytz>=2021.1",
]

[project.optional-dependencies]
smb = [
    "smbprotocol>=1.8.0",
    "pysmb>=1.2.0",
]
dev = [
    "pytest>=6.0.0",
    "pytest-qt>=4.0.0",
    "black>=21.0.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
]
docs = [
    "sphinx>=4.0.0",
    "sphinx-rtd-theme>=0.5.0",
]

[project.urls]
Homepage = "https://github.com/jackycj0830/ExchangeLogAnalyzer"
Repository = "https://github.com/jackycj0830/ExchangeLogAnalyzer.git"
Documentation = "https://github.com/jackycj0830/ExchangeLogAnalyzer/wiki"
"Bug Tracker" = "https://github.com/jackycj0830/ExchangeLogAnalyzer/issues"

[project.scripts]
exchange-log-analyzer = "exchange_log_analyzer.main:main"

[project.gui-scripts]
exchange-log-analyzer-gui = "exchange_log_analyzer.main:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
exchange_log_analyzer = ["*.ico", "*.svg"]

[tool.black]
line-length = 88
target-version = ['py37', 'py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".tox",
    ".venv",
]

[tool.mypy]
python_version = "3.7"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
