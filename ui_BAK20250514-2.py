from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit,
    QLabel, QDateEdit, QTimeEdit, QFileDialog, QTableWidget, QTableWidgetItem,
    QPlainTextEdit, QMessageBox, QMainWindow, QAction
)
from PyQt5.QtCore import QDate, QTime, QDateTime, Qt
from PyQt5.QtGui import QFont
from parser import parse_log, filter_logs
import pandas as pd
import os
import sys
import datetime
import re

class LogAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📊 Exchange 追蹤日誌分析工具 v1.0.0 (2025-05-14) By Jacky.Zou")
        self.setGeometry(100, 100, 1100, 700)

        self.df = pd.DataFrame()

        # 📁 工具列
        menubar = self.menuBar()
        file_menu = menubar.addMenu("檔案")

        open_action = QAction("開啟 LOG 資料夾", self)
        open_action.triggered.connect(self.load_logs)
        file_menu.addAction(open_action)

        export_action = QAction("匯出查詢結果為 CSV", self)
        export_action.triggered.connect(self.export_csv)
        file_menu.addAction(export_action)

        exit_action = QAction("結束程式", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 📦 主畫面元件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout()

        # 日期時間搜尋區（分開）
        date_layout = QHBoxLayout()

        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDisplayFormat("yyyy/MM/dd")
        self.start_date.setDate(QDate.currentDate().addDays(-7))

        self.start_time = QTimeEdit()
        self.start_time.setDisplayFormat("HH:mm")
        self.start_time.setTime(QTime(0, 0))

        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDisplayFormat("yyyy/MM/dd")
        self.end_date.setDate(QDate.currentDate())

        self.end_time = QTimeEdit()
        self.end_time.setDisplayFormat("HH:mm")
        self.end_time.setTime(QTime(23, 59))

        date_layout.addWidget(QLabel("起始日期:"))
        date_layout.addWidget(self.start_date)
        date_layout.addWidget(QLabel("起始時間:"))
        date_layout.addWidget(self.start_time)
        date_layout.addSpacing(20)
        date_layout.addWidget(QLabel("結束日期:"))
        date_layout.addWidget(self.end_date)
        date_layout.addWidget(QLabel("結束時間:"))
        date_layout.addWidget(self.end_time)

        layout.addLayout(date_layout)

        # 關鍵字輸入
        self.keyword_input = QLineEdit()
        self.keyword_input.setPlaceholderText("輸入關鍵字（如寄件人、收件人、主旨、IP、EventId 等）")
        layout.addWidget(self.keyword_input)

        # 按鈕列
        btn_layout = QHBoxLayout()
        load_btn = QPushButton("📂 選擇資料夾並載入 LOG")
        load_btn.clicked.connect(self.load_logs)
        btn_layout.addWidget(load_btn)

        filter_btn = QPushButton("🔍 執行查詢")
        filter_btn.clicked.connect(self.apply_filter)
        btn_layout.addWidget(filter_btn)

        export_btn = QPushButton("📤 匯出查詢結果為 CSV")
        export_btn.clicked.connect(self.export_csv)
        btn_layout.addWidget(export_btn)

        layout.addLayout(btn_layout)

        # 初始版權資訊區
        self.info_box = QPlainTextEdit()
        self.info_box.setReadOnly(True)
        self.info_box.setLineWrapMode(QPlainTextEdit.NoWrap)
        font = QFont("Courier New")
        font.setPointSize(10)
        self.info_box.setFont(font)
        self.info_box.setPlainText(self.get_default_info())
        layout.addWidget(self.info_box)

        # 表格顯示區
        self.table = QTableWidget()
        self.table.hide()
        layout.addWidget(self.table)

        central_widget.setLayout(layout)

    def get_default_info(self):
        version_path = os.path.join(os.path.dirname(__file__), "version.txt")
        try:
            with open(version_path, "r", encoding="utf-8") as f:
                return f.read()
        except Exception:
            return "❌ 無法讀取版權資訊 version.txt，請確認檔案存在。"

    def extract_datetime_from_filename(self, filename):
        """
        解析 MSGTRKYYYYMMDDHH(-X).LOG → 回傳 datetime
        """
        try:
            match = re.search(r"MSGTRK(\d{10})", filename.upper())
            if match:
                datetime_str = match.group(1)
                return datetime.datetime.strptime(datetime_str, "%Y%m%d%H")
            return None
        except Exception:
            return None

    def load_logs(self):
        folder_path = QFileDialog.getExistingDirectory(self, "選擇含 Exchange LOG 檔的資料夾")
        if not folder_path:
            return

        all_files = [f for f in os.listdir(folder_path)
                     if f.lower().endswith(".log") and os.path.isfile(os.path.join(folder_path, f))]

        start_dt = QDateTime(self.start_date.date(), self.start_time.time()).toPyDateTime()
        end_dt = QDateTime(self.end_date.date(), self.end_time.time()).toPyDateTime()

        matching_files = []

        for filename in all_files:
            file_dt = self.extract_datetime_from_filename(filename)
            if file_dt and start_dt <= file_dt <= end_dt:
                matching_files.append(os.path.join(folder_path, filename))

        if not matching_files:
            QMessageBox.warning(self, "⚠️ 找不到符合時間的 LOG", "沒有檔案符合選定的日期與時間區間。")
            return

        all_dfs = [parse_log(f) for f in matching_files]
        self.df = pd.concat(all_dfs, ignore_index=True)

        self.info_box.hide()
        self.table.show()
        self.show_data(self.df)

    def apply_filter(self):
        if self.df.empty:
            QMessageBox.information(self, "請先載入 LOG", "請先載入 Exchange LOG 檔案。")
            return

        keyword = self.keyword_input.text()
        start_dt = QDateTime(self.start_date.date(), self.start_time.time()).toPyDateTime()
        end_dt = QDateTime(self.end_date.date(), self.end_time.time()).toPyDateTime()

        result = filter_logs(self.df, keyword, start_dt, end_dt)
        self.show_data(result)

    def show_data(self, df):
        self.table.setRowCount(len(df))
        self.table.setColumnCount(len(df.columns))
        self.table.setHorizontalHeaderLabels(df.columns)

        for i in range(len(df)):
            for j in range(len(df.columns)):
                self.table.setItem(i, j, QTableWidgetItem(str(df.iat[i, j])))

        self.filtered_df = df

    def export_csv(self):
        if not hasattr(self, 'filtered_df') or self.filtered_df.empty:
            QMessageBox.information(self, "⚠️ 無資料", "請先查詢再匯出。")
            return
        path, _ = QFileDialog.getSaveFileName(self, "儲存 CSV 檔案", filter="CSV Files (*.csv)")
        if path:
            self.filtered_df.to_csv(path, index=False)
            QMessageBox.information(self, "✅ 匯出成功", f"已匯出：\n{path}")
