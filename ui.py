# ✅ 完整整合版：自動翻譯表頭、自適應欄位、自動換行、匯出中文 CSV
# ✅ 含進度條、查詢筆數過多提醒與背景執行緒功能（含中斷與記憶體優化）
import sys
import os
import datetime
import re
import pytz
import gc
import shutil
import pandas as pd

from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit,
    QLabel, QDateEdit, QTimeEdit, QFileDialog, QTableWidget, QTableWidgetItem,
    QMessageBox, QMainWindow, QAction, QComboBox, QDialog, QTextEdit,
    QDialogButtonBox, QMenu, QProgressDialog, QInputDialog
)
from PyQt5.QtCore import QDate, QTime, QDateTime, Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from smbprotocol.connection import Connection
from smbprotocol.session import Session
from smbprotocol.tree import TreeConnect
from smbprotocol.open import Open, CreateDisposition, ShareAccess, FilePipePrinterAccessMask, DirectoryAccessMask
from smbprotocol.exceptions import SMBAuthenticationError
from smb.SMBConnection import SMBConnection

from parser import parse_log, filter_logs
from version import VERSION_INFO_HTML

class SmbDownloadDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("連線至 Exchange - SMB 模式 (pysmb)")
        self.setMinimumWidth(650)
        self.files = []
        self.conn = None
        self.server = ''
        self.share = 'LogsMessageTracking'

        layout = QVBoxLayout()
        form_layout = QHBoxLayout()
        self.server_input = QLineEdit()
        self.server_input.setPlaceholderText("伺服器名稱或IP")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("帳號")
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("密碼")
        self.password_input.setEchoMode(QLineEdit.Password)

        form_layout.addWidget(QLabel("伺服器："))
        form_layout.addWidget(self.server_input)
        form_layout.addWidget(QLabel("帳號："))
        form_layout.addWidget(self.username_input)
        form_layout.addWidget(QLabel("密碼："))
        form_layout.addWidget(self.password_input)
        layout.addLayout(form_layout)

        self.connect_btn = QPushButton("連線")
        self.connect_btn.clicked.connect(self.connect_smb)
        layout.addWidget(self.connect_btn)

        self.file_list = QTableWidget()
        self.file_list.setColumnCount(2)
        self.file_list.setHorizontalHeaderLabels(["", "檔案名稱"])
        self.file_list.setSelectionMode(QTableWidget.MultiSelection)
        layout.addWidget(self.file_list)

        btn_layout = QHBoxLayout()
        self.select_all_btn = QPushButton("全部勾選")
        self.select_all_btn.clicked.connect(self.select_all_files)
        btn_layout.addWidget(self.select_all_btn)
        self.download_btn = QPushButton("下載已勾選項目")
        self.download_btn.clicked.connect(self.download_files)
        btn_layout.addWidget(self.download_btn)
        layout.addLayout(btn_layout)

        self.setLayout(layout)

    def connect_smb(self):
        self.server = self.server_input.text().strip()
        if not self.server:
            QMessageBox.warning(self, "請輸入伺服器", "請輸入正確的伺服器名稱或IP！")
            return
        username = self.username_input.text().strip()
        if not username:
            QMessageBox.warning(self, "請輸入帳號", "請輸入正確的帳號（建議 domain\\username）！")
            return
        password = self.password_input.text().strip()
        if not password:
            QMessageBox.warning(self, "請輸入密碼", "請輸入正確的密碼！")
            return

        # 分離 domain 和 username（pysmb 須分開）
        domain = ""
        if "\\" in username:
            domain, username = username.split("\\", 1)
        else:
            domain = ""

        # 設定本機 NetBIOS 名稱（亂填也可以）
        client_machine_name = "PyClient"
        try:
            self.conn = SMBConnection(
                username, password, client_machine_name, self.server,
                domain=domain, use_ntlm_v2=True, is_direct_tcp=True
            )
            connected = self.conn.connect(self.server, 445)
            if not connected:
                raise Exception("SMB 連線失敗")
            # 列出檔案
            files = self.conn.listPath(self.share, '/')
            logs = []
            for f in files:
                if not f.isDirectory and f.filename.lower().endswith('.log'):
                    logs.append(f.filename)
            self.files = logs
            self.populate_file_list()
            QMessageBox.information(self, "連線成功", f"共找到 {len(logs)} 個 LOG 檔案。")
        except Exception as e:
            QMessageBox.critical(self, "連線失敗", f"無法連線或目錄不存在：\n{str(e)}")

    def populate_file_list(self):
        self.file_list.setRowCount(len(self.files))
        for i, fname in enumerate(self.files):
            checkbox = QTableWidgetItem()
            checkbox.setFlags(checkbox.flags() | Qt.ItemIsUserCheckable)
            checkbox.setCheckState(Qt.Unchecked)
            self.file_list.setItem(i, 0, checkbox)
            self.file_list.setItem(i, 1, QTableWidgetItem(fname))

    def select_all_files(self):
        for i in range(self.file_list.rowCount()):
            item = self.file_list.item(i, 0)
            if item:
                item.setCheckState(Qt.Checked)

    def download_files(self):
        if self.conn is None:
            QMessageBox.warning(self, "請先連線", "請先連線至 SMB 伺服器。")
            return
        selected = []
        for i in range(self.file_list.rowCount()):
            if self.file_list.item(i, 0).checkState() == Qt.Checked:
                selected.append(self.file_list.item(i, 1).text())
        if not selected:
            QMessageBox.information(self, "未選擇檔案", "請先勾選要下載的檔案。")
            return
        out_dir = QFileDialog.getExistingDirectory(self, "選擇下載目錄")
        if not out_dir:
            return

        progress = QProgressDialog("正在下載...", "取消", 0, len(selected), self)
        progress.setWindowModality(Qt.WindowModal)
        progress.setValue(0)
        for idx, fname in enumerate(selected):
            if progress.wasCanceled():
                break
            try:
                with open(os.path.join(out_dir, fname), "wb") as f:
                    self.conn.retrieveFile(self.share, "/" + fname, f)
            except Exception as e:
                QMessageBox.warning(self, "下載失敗", f"下載檔案 {fname} 失敗：\n{e}")
            progress.setValue(idx + 1)
        progress.close()
        QMessageBox.information(self, "下載完成", f"已下載 {len(selected)} 個檔案到 {out_dir}")

# ============ 後台載入執行緒與主程式UI ============

class LoadWorker(QThread):
    progress_update = pyqtSignal(int)
    result_ready = pyqtSignal(pd.DataFrame)
    canceled = False
    def __init__(self, file_paths, timezone):
        super().__init__()
        self.file_paths = file_paths
        self.timezone = timezone
    def run(self):
        dfs = []
        for idx, path in enumerate(self.file_paths):
            if self.canceled:
                break
            df = parse_log(path, timezone=self.timezone)
            dfs.append(df)
            self.progress_update.emit(idx + 1)
        if self.canceled:
            self.result_ready.emit(pd.DataFrame())
        else:
            combined = pd.concat(dfs, ignore_index=True)
            self.result_ready.emit(combined)
        del dfs
        gc.collect()
    def stop(self):
        self.canceled = True

class LogAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Exchange 追蹤日誌分析工具 v1.0.9 (2025-07-19)")
        self.setWindowIcon(QIcon())
        self.setGeometry(100, 100, 1200, 800)
        self.worker = None
        self.progress = None
        self.df = pd.DataFrame()
        self.filtered_df = pd.DataFrame()
        self.current_page = 0
        self.rows_per_page = 100
        self.selected_timezone = 'UTC'
        self.show_extra_columns = False
        self.display_columns = [
            "date-time", "client-ip", "client-hostname",
            "server-ip", "server-hostname", "recipient-address", "sender-address",
            "message-subject"
        ]
        menubar = self.menuBar()
        file_menu = menubar.addMenu("檔案")
        smb_action = QAction("連線至 Exchange 下載 Log", self)
        smb_action.triggered.connect(self.open_smb_dialog)
        file_menu.addAction(smb_action)
        open_action = QAction("選擇資料夾並載入 LOG", self)
        open_action.triggered.connect(self.load_logs)
        file_menu.addAction(open_action)
        open_file_action = QAction("開啟指定 LOG 檔案", self)
        open_file_action.triggered.connect(self.load_specific_log_files)
        file_menu.addAction(open_file_action)
        export_action = QAction("匯出查詢結果為 CSV", self)
        export_action.triggered.connect(self.export_csv)
        file_menu.addAction(export_action)
        exit_action = QAction("結束程式", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        help_menu = menubar.addMenu("說明")
        version_action = QAction("版本說明", self)
        version_action.triggered.connect(self.show_version_info)
        help_menu.addAction(version_action)

        # -------------- UI區 -------------------
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout()
        date_layout = QHBoxLayout()
        self.start_date = QDateEdit(calendarPopup=True)
        self.start_date.setDisplayFormat("yyyy/MM/dd")
        self.start_date.setDate(QDate.currentDate().addDays(-7))
        self.start_time = QTimeEdit()
        self.start_time.setDisplayFormat("HH:mm")
        self.start_time.setTime(QTime(0, 0))
        self.end_date = QDateEdit(calendarPopup=True)
        self.end_date.setDisplayFormat("yyyy/MM/dd")
        self.end_date.setDate(QDate.currentDate())
        self.end_time = QTimeEdit()
        self.end_time.setDisplayFormat("HH:mm")
        self.end_time.setTime(QTime(23, 59))
        self.timezone_selector = QComboBox()
        self.timezone_selector.addItems(pytz.all_timezones)
        self.timezone_selector.setCurrentText('UTC')
        self.timezone_selector.currentTextChanged.connect(self.set_timezone)
        date_layout.addWidget(QLabel("起始日期:"))
        date_layout.addWidget(self.start_date)
        date_layout.addWidget(QLabel("起始時間:"))
        date_layout.addWidget(self.start_time)
        date_layout.addSpacing(20)
        date_layout.addWidget(QLabel("結束日期:"))
        date_layout.addWidget(self.end_date)
        date_layout.addWidget(QLabel("結束時間:"))
        date_layout.addWidget(self.end_time)
        date_layout.addSpacing(20)
        date_layout.addWidget(QLabel("顯示時區:"))
        date_layout.addWidget(self.timezone_selector)
        layout.addLayout(date_layout)
        self.keyword_input = QLineEdit()
        self.keyword_input.setPlaceholderText("輸入關鍵字（如寄件人、收件人、主旨、IP、EventId 等）")
        layout.addWidget(self.keyword_input)
        btn_layout = QHBoxLayout()
        specific_file_btn = QPushButton("📂 開啟指定 LOG 檔案")
        specific_file_btn.clicked.connect(self.load_specific_log_files)
        btn_layout.addWidget(specific_file_btn)
        load_btn = QPushButton("\U0001F4C2 選擇資料夾並載入 LOG")
        load_btn.clicked.connect(self.load_logs)
        btn_layout.addWidget(load_btn)
        filter_btn = QPushButton("\U0001F50D 執行查詢")
        filter_btn.clicked.connect(self.apply_filter)
        btn_layout.addWidget(filter_btn)
        export_btn = QPushButton("\U0001F4E4 匯出查詢結果為 CSV")
        export_btn.clicked.connect(self.export_csv)
        btn_layout.addWidget(export_btn)
        toggle_btn = QPushButton("顯示/隱藏額外欄位")
        toggle_btn.clicked.connect(self.toggle_extra_columns)
        btn_layout.addWidget(toggle_btn)
        reset_btn = QPushButton("🔄 重置查詢")
        reset_btn.clicked.connect(self.reset_filter)
        btn_layout.addWidget(reset_btn)
        layout.addLayout(btn_layout)
        self.info_box = QTextEdit()
        self.info_box.setReadOnly(True)
        font = QFont("Microsoft JhengHei", 10)
        self.info_box.setFont(font)
        self.info_box.setHtml(VERSION_INFO_HTML)
        layout.addWidget(self.info_box)
        pagination_layout = QHBoxLayout()
        self.page_label = QLabel("頁數：0 / 0")
        self.prev_button = QPushButton("⬅ 上一頁")
        self.next_button = QPushButton("➡ 下一頁")
        self.page_size_selector = QComboBox()
        self.page_size_selector.addItems(["100", "200", "300", "500", "1000", "2000"])
        self.page_size_selector.setCurrentText("100")
        self.page_size_selector.currentIndexChanged.connect(self.change_page_size)
        self.prev_button.clicked.connect(self.go_to_prev_page)
        self.next_button.clicked.connect(self.go_to_next_page)
        pagination_layout.addWidget(self.prev_button)
        pagination_layout.addWidget(self.next_button)
        pagination_layout.addWidget(QLabel("每頁筆數:"))
        pagination_layout.addWidget(self.page_size_selector)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.page_label)
        layout.addLayout(pagination_layout)
        self.table = QTableWidget()
        self.table.hide()
        layout.addWidget(self.table)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.setSelectionBehavior(QTableWidget.SelectItems)
        self.table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_table_context_menu)
        self.table.horizontalHeader().sectionClicked.connect(self.on_header_clicked)
        central_widget.setLayout(layout)

    def open_smb_dialog(self):
        dlg = SmbDownloadDialog(self)
        dlg.exec_()

    def set_timezone(self, tz):
        self.selected_timezone = tz

    def show_progress_dialog(self, label_text, maximum):
        progress = QProgressDialog(label_text, "取消", 0, maximum, self)
        progress.setWindowTitle("處理中...")
        progress.setWindowModality(Qt.WindowModal)
        progress.setMinimumDuration(0)
        progress.setValue(0)
        progress.canceled.connect(self.cancel_worker)
        return progress

    def cancel_worker(self):
        if self.worker:
            self.worker.stop()

    def check_large_query(self, count, threshold=100000):
        if count > threshold:
            reply = QMessageBox.question(
                self,
                "⚠️ 資料筆數過多",
                f"查詢結果有 {count:,} 筆，可能造成系統卡頓或當機。\n是否繼續？",
                QMessageBox.Yes | QMessageBox.No
            )
            return reply == QMessageBox.Yes
        return True

    def load_specific_log_files(self):
        file_paths, _ = QFileDialog.getOpenFileNames(self, "選擇 LOG 檔案", "", "Log Files (*.log);;All Files (*)")
        if not file_paths:
            return
        self.progress = self.show_progress_dialog("正在載入 LOG 檔案...", len(file_paths))
        self.worker = LoadWorker(file_paths, self.selected_timezone)
        self.worker.progress_update.connect(self.progress.setValue)
        self.worker.result_ready.connect(self.on_load_finished)
        self.worker.start()

    def load_logs(self):
        folder_path = QFileDialog.getExistingDirectory(self, "選擇含 Exchange LOG 檔的資料夾")
        if not folder_path:
            return
        all_files = [f for f in os.listdir(folder_path)
                     if f.lower().endswith(".log") and os.path.isfile(os.path.join(folder_path, f))]
        start_dt = QDateTime(self.start_date.date(), self.start_time.time()).toPyDateTime()
        end_dt = QDateTime(self.end_date.date(), self.end_time.time()).toPyDateTime()
        matching_files = []
        for filename in all_files:
            file_dt = self.extract_datetime_from_filename(filename)
            if file_dt and start_dt <= file_dt <= end_dt:
                matching_files.append(os.path.join(folder_path, filename))
        if not matching_files:
            QMessageBox.warning(self, "⚠️ 找不到符合時間的 LOG", "沒有檔案符合選定的日期與時間區間。")
            return
        all_dfs = [parse_log(f, timezone=self.selected_timezone) for f in matching_files]
        self.df = pd.concat(all_dfs, ignore_index=True)
        self.apply_filter()

    def extract_datetime_from_filename(self, filename):
        try:
            match = re.search(r"MSGTRK(\d{10})", filename.upper())
            if match:
                datetime_str = match.group(1)
                return datetime.datetime.strptime(datetime_str, "%Y%m%d%H")
            return None
        except Exception:
            return None

    def apply_filter(self):
        if self.df.empty:
            QMessageBox.information(self, "請先載入 LOG", "請先載入 Exchange LOG 檔案。")
            return
        keyword = self.keyword_input.text()
        start_dt = QDateTime(self.start_date.date(), self.start_time.time()).toPyDateTime()
        end_dt = QDateTime(self.end_date.date(), self.end_time.time()).toPyDateTime()
        filtered = filter_logs(self.df, keyword, start_dt, end_dt)
        if not self.check_large_query(len(filtered)):
            return
        self.filtered_df = filtered
        self.current_page = 0
        self.update_table_page()

    def on_header_clicked(self, logicalIndex):
        if self.filtered_df.empty:
            return
        if not self.show_extra_columns:
            cols = [col for col in self.display_columns if col in self.filtered_df.columns]
        else:
            cols = self.display_columns + [col for col in self.filtered_df.columns if col not in self.display_columns]
        if logicalIndex >= len(cols):
            return
        col_name = cols[logicalIndex]
        text, ok = QInputDialog.getText(self, "欄位進階查詢", f"請輸入要查詢的關鍵字（將篩選「{col_name}」欄位）：")
        if ok and text:
            self.apply_column_filter(col_name, text)

    def apply_column_filter(self, column, keyword):
        if self.filtered_df.empty or column not in self.filtered_df.columns:
            return
        filtered = self.filtered_df[self.filtered_df[column].astype(str).str.contains(keyword, case=False, na=False)]
        self.filtered_df = filtered
        self.current_page = 0
        self.update_table_page()

    def reset_filter(self):
        self.apply_filter()

    def toggle_extra_columns(self):
        self.show_extra_columns = not self.show_extra_columns
        self.update_table_page()

    def update_table_page(self):
        translation_map = {
            "date-time": "日期時間", "client-ip": "用戶端IP", "client-hostname": "用戶端主機名稱",
            "server-ip": "伺服器IP", "server-hostname": "伺服器主機名稱", "source-context": "來源上下文",
            "connector-id": "連接器ID", "source": "來源", "event-id": "事件ID",
            "internal-message-id": "內部訊息ID", "message-id": "訊息ID", "network-message-id": "網路訊息ID",
            "recipient-address": "收件人地址", "recipient-status": "收件人狀態", "total-bytes": "總位元組數",
            "recipient-count": "收件人數量", "related-recipient-address": "相關收件人地址", "reference": "參考",
            "message-subject": "主旨", "sender-address": "寄件人地址", "return-path": "退信路徑",
            "message-info": "訊息資訊", "directionality": "方向性", "tenant-id": "租戶ID",
            "original-client-ip": "原始用戶端IP", "original-server-ip": "原始伺服器IP", "custom-data": "自訂資料",
            "transport-traffic-type": "傳輸流量類型", "log-id": "日誌ID", "schema-version": "結構版本"
        }
        total_rows = len(self.filtered_df)
        rows_per_page = self.rows_per_page
        total_pages = (total_rows - 1) // rows_per_page + 1 if total_rows else 1
        self.page_label.setText(f"頁數：{self.current_page + 1} / {total_pages}（總共 {total_rows} 筆）")
        start = self.current_page * rows_per_page
        end = min(start + rows_per_page, total_rows)
        df = self.filtered_df.iloc[start:end]
        if not self.show_extra_columns:
            df = df[[col for col in self.display_columns if col in df.columns]]
        else:
            cols = self.display_columns + [col for col in df.columns if col not in self.display_columns]
            df = df[cols]
        self.table.setRowCount(len(df))
        self.table.setColumnCount(len(df.columns))
        translated_headers = [translation_map.get(col, col) for col in df.columns]
        self.table.setHorizontalHeaderLabels(translated_headers)
        for i in range(len(df)):
            for j in range(len(df.columns)):
                content = str(df.iat[i, j])
                item = QTableWidgetItem(content)
                item.setFlags(item.flags() ^ Qt.ItemIsEditable)
                item.setTextAlignment(Qt.AlignLeft | Qt.AlignTop)
                item.setFont(QFont("Microsoft JhengHei", 8))
                self.table.setItem(i, j, item)
        self.table.resizeColumnsToContents()
        self.table.resizeRowsToContents()
        self.table.show()
        self.info_box.hide()

    def go_to_prev_page(self):
        if self.current_page > 0:
            self.current_page -= 1
            self.update_table_page()

    def go_to_next_page(self):
        max_page = (len(self.filtered_df) - 1) // self.rows_per_page
        if self.current_page < max_page:
            self.current_page += 1
            self.update_table_page()

    def change_page_size(self):
        self.rows_per_page = int(self.page_size_selector.currentText())
        self.current_page = 0
        self.update_table_page()

    def export_csv(self):
        if self.filtered_df.empty:
            QMessageBox.information(self, "⚠️ 無資料", "請先查詢再匯出。")
            return
        translation_map = {
            "date-time": "日期時間", "client-ip": "用戶端IP", "client-hostname": "用戶端主機名稱",
            "server-ip": "伺服器IP", "server-hostname": "伺服器主機名稱", "source-context": "來源上下文",
            "connector-id": "連接器ID", "source": "來源", "event-id": "事件ID",
            "internal-message-id": "內部訊息ID", "message-id": "訊息ID", "network-message-id": "網路訊息ID",
            "recipient-address": "收件人地址", "recipient-status": "收件人狀態", "total-bytes": "總位元組數",
            "recipient-count": "收件人數量", "related-recipient-address": "相關收件人地址", "reference": "參考",
            "message-subject": "主旨", "sender-address": "寄件人地址", "return-path": "退信路徑",
            "message-info": "訊息資訊", "directionality": "方向性", "tenant-id": "租戶ID",
            "original-client-ip": "原始用戶端IP", "original-server-ip": "原始伺服器IP", "custom-data": "自訂資料",
            "transport-traffic-type": "傳輸流量類型", "log-id": "日誌ID", "schema-version": "結構版本"
        }
        translated_df = self.filtered_df.rename(columns=translation_map)
        path, _ = QFileDialog.getSaveFileName(self, "儲存 CSV 檔案", filter="CSV Files (*.csv)")
        if path:
            translated_df.to_csv(path, index=False, encoding='utf-8-sig')
            QMessageBox.information(self, "✅ 匯出成功", f"已匯出：\n{path}")

    def show_version_info(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("版本說明")
        dialog.setMinimumWidth(1200)
        dialog.setMinimumHeight(700)
        layout = QVBoxLayout()
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setHtml(VERSION_INFO_HTML)
        text_edit.setFont(QFont("Courier New", 10))
        layout.addWidget(text_edit)
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(dialog.accept)
        layout.addWidget(button_box)
        dialog.setLayout(layout)
        dialog.exec_()

    def show_table_context_menu(self, pos):
        menu = QMenu()
        copy_action = menu.addAction("複製")
        action = menu.exec_(self.table.viewport().mapToGlobal(pos))
        if action == copy_action:
            self.copy_selected_cells()

    def copy_selected_cells(self):
        selected_ranges = self.table.selectedRanges()
        if not selected_ranges:
            return
        copied_text = ""
        for range_ in selected_ranges:
            for row in range(range_.topRow(), range_.bottomRow() + 1):
                row_text = []
                for col in range(range_.leftColumn(), range_.rightColumn() + 1):
                    item = self.table.item(row, col)
                    row_text.append(item.text() if item else "")
                copied_text += "\t".join(row_text) + "\n"
        QApplication.clipboard().setText(copied_text.strip())

    def on_load_finished(self, df):
        self.df = df
        self.progress.close()
        self.apply_filter()

# ======== 程式進入點 =======
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = LogAnalyzer()
    window.show()
    sys.exit(app.exec_())
# 這是完整的 Exchange Log Analyzer 程式碼，包含了SMB下載功能、查詢與匯出CSV等功能。