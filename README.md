# Exchange Log Analyzer

[![Python Version](https://img.shields.io/badge/python-3.7%2B-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)](https://github.com/jackycj0830/ExchangeLogAnalyzer)

一個功能強大的 Microsoft Exchange 郵件追蹤日誌分析工具，提供直觀的圖形化介面，讓系統管理員能夠輕鬆分析和查詢 Exchange Server 的訊息追蹤日誌。

## 📋 目錄

- [功能特色](#-功能特色)
- [系統需求](#-系統需求)
- [安裝方式](#-安裝方式)
- [使用方法](#-使用方法)
- [專案結構](#-專案結構)
- [開發指南](#-開發指南)
- [技術架構](#-技術架構)
- [擴充功能](#-擴充功能)
- [常見問題](#-常見問題)
- [貢獻指南](#-貢獻指南)
- [授權條款](#-授權條款)
- [聯絡資訊](#-聯絡資訊)

## 🚀 功能特色

### 核心功能
- **📁 多檔案載入**: 支援批次載入多個 .log 檔案
- **🔍 智慧搜尋**: 關鍵字模糊搜尋，支援多欄位查詢
- **📅 日期範圍篩選**: 精確的日期時間範圍篩選功能
- **📊 資料視覺化**: 清晰的表格顯示，支援排序和欄位調整
- **💾 資料匯出**: 支援匯出為 CSV 格式，保留中文編碼

### 進階功能
- **🌐 SMB 遠端連線**: 支援透過 SMB 協定連線至 Exchange 伺服器下載日誌
- **⚡ 背景處理**: 多執行緒處理大型檔案，避免介面凍結
- **📈 進度顯示**: 即時顯示檔案處理進度
- **🌍 時區支援**: 自動處理時區轉換
- **🔄 記憶體優化**: 智慧記憶體管理，處理大型日誌檔案

### 使用者介面
- **🎨 現代化 GUI**: 基於 PyQt5 的直觀使用者介面
- **📱 響應式設計**: 自適應視窗大小和解析度
- **🌙 使用者友善**: 中文介面，符合本地化需求
- **⚙️ 可自訂設定**: 支援個人化設定和偏好

## 💻 系統需求

### 最低需求
- **作業系統**: Windows 7/10/11, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Python 版本**: Python 3.7 或更高版本
- **記憶體**: 最少 4GB RAM (建議 8GB 以上)
- **硬碟空間**: 100MB 可用空間

### 建議需求
- **作業系統**: Windows 10/11, macOS 11+, Linux (Ubuntu 20.04+)
- **Python 版本**: Python 3.9 或更高版本
- **記憶體**: 8GB RAM 或更多
- **硬碟空間**: 500MB 可用空間

## 📦 安裝方式

### 方法一：使用 pip 安裝（推薦）

```bash
# 安裝基本版本
pip install exchange-log-analyzer

# 安裝包含 SMB 功能的完整版本
pip install exchange-log-analyzer[smb]

# 安裝開發版本（包含所有依賴）
pip install exchange-log-analyzer[smb,dev]
```

### 方法二：從原始碼安裝

```bash
# 1. 複製專案
git clone https://github.com/jackycj0830/ExchangeLogAnalyzer.git
cd ExchangeLogAnalyzer

# 2. 建立虛擬環境（推薦）
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate

# 3. 安裝依賴套件
pip install -r requirements.txt

# 4. 安裝專案
pip install -e .
```

### 方法三：使用 Poetry（開發者推薦）

```bash
# 1. 安裝 Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 2. 複製專案並安裝
git clone https://github.com/jackycj0830/ExchangeLogAnalyzer.git
cd ExchangeLogAnalyzer
poetry install

# 3. 啟動虛擬環境
poetry shell
```

## 🎯 使用方法

### 快速開始

```bash
# 啟動應用程式
exchange-log-analyzer

# 或者使用 Python 模組方式
python -m exchange_log_analyzer

# 從原始碼執行
python main_new.py
```

### 基本操作流程

1. **啟動應用程式**
   ```bash
   exchange-log-analyzer
   ```

2. **載入日誌檔案**
   - 點擊「瀏覽資料夾」選擇包含 .log 檔案的目錄
   - 或使用「連線至 Exchange 下載 Log」功能遠端下載

3. **設定搜尋條件**
   - 輸入關鍵字（支援模糊搜尋）
   - 設定日期時間範圍
   - 點擊「搜尋」執行查詢

4. **檢視結果**
   - 在表格中檢視搜尋結果
   - 支援欄位排序和調整
   - 可複製特定資料

5. **匯出資料**
   - 點擊「匯出 CSV」儲存結果
   - 支援 UTF-8 編碼，確保中文正確顯示

### 進階功能使用

#### SMB 遠端連線
```
1. 選擇「檔案」→「連線至 Exchange 下載 Log」
2. 輸入伺服器資訊：
   - 伺服器名稱或 IP
   - 使用者帳號
   - 密碼
3. 選擇要下載的日誌檔案
4. 下載完成後自動載入分析
```

#### 批次處理大型檔案
```
1. 選擇包含多個 .log 檔案的資料夾
2. 應用程式會自動在背景處理所有檔案
3. 顯示處理進度和狀態
4. 完成後合併所有資料供查詢
```

## 📁 專案結構

```
ExchangeLogAnalyzer/
├── src/
│   └── exchange_log_analyzer/
│       ├── __init__.py              # 套件初始化
│       ├── main.py                  # 主程式入口
│       ├── core/                    # 核心功能模組
│       │   ├── __init__.py
│       │   ├── parser.py            # 日誌解析器
│       │   ├── config.py            # 設定管理
│       │   └── version.py           # 版本資訊
│       ├── gui/                     # 圖形介面模組
│       │   ├── __init__.py
│       │   ├── main_window.py       # 主視窗
│       │   ├── dialogs.py           # 對話框
│       │   └── workers.py           # 背景工作執行緒
│       └── utils/                   # 工具函式模組
│           ├── __init__.py
│           ├── helpers.py           # 輔助函式
│           └── constants.py         # 常數定義
├── tests/                           # 測試檔案
│   ├── unit/                        # 單元測試
│   └── integration/                 # 整合測試
├── docs/                            # 文件目錄
├── examples/                        # 範例檔案
├── scripts/                         # 工具腳本
├── requirements.txt                 # 依賴套件清單
├── setup.py                         # 安裝腳本
├── pyproject.toml                   # 專案設定檔
├── README.md                        # 專案說明文件
├── LICENSE                          # 授權條款
├── .gitignore                       # Git 忽略檔案
└── main_new.py                      # 主程式啟動檔
```

## 🛠 開發指南

### 開發環境設置

```bash
# 1. 複製專案
git clone https://github.com/jackycj0830/ExchangeLogAnalyzer.git
cd ExchangeLogAnalyzer

# 2. 建立開發環境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 3. 安裝開發依賴
pip install -e .[dev,smb]

# 4. 安裝 pre-commit hooks
pre-commit install
```

### 程式碼品質

```bash
# 程式碼格式化
black src/ tests/

# 程式碼檢查
flake8 src/ tests/

# 型別檢查
mypy src/

# 執行測試
pytest tests/
```

### 建立新功能

1. **建立功能分支**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **撰寫程式碼**
   - 遵循 PEP 8 程式碼風格
   - 添加適當的文件字串
   - 撰寫對應的測試

3. **測試功能**
   ```bash
   pytest tests/ -v
   ```

4. **提交變更**
   ```bash
   git add .
   git commit -m "Add new feature: description"
   git push origin feature/new-feature
   ```

## 🏗 技術架構

### 核心技術棧

| 技術 | 版本 | 用途 |
|------|------|------|
| **Python** | 3.7+ | 主要程式語言 |
| **PyQt5** | 5.15+ | 圖形使用者介面框架 |
| **pandas** | 1.3+ | 資料處理和分析 |
| **pytz** | 2021.1+ | 時區處理 |
| **smbprotocol** | 1.8+ | SMB 協定支援（選用） |
| **pysmb** | 1.2+ | 替代 SMB 實作（選用） |

### 架構設計原則

- **模組化設計**: 清楚分離核心邏輯、GUI 和工具函式
- **單一職責**: 每個模組專注於特定功能
- **可擴展性**: 易於添加新功能和支援格式
- **錯誤處理**: 完善的例外處理和使用者回饋
- **效能優化**: 多執行緒處理和記憶體管理

### 資料流程

```mermaid
graph TD
    A[使用者選擇檔案] --> B[LogParser 解析]
    B --> C[資料驗證和清理]
    C --> D[儲存至 DataFrame]
    D --> E[使用者設定篩選條件]
    E --> F[filter_logs 篩選]
    F --> G[更新 GUI 表格]
    G --> H[使用者匯出結果]
```

## 🔧 擴充功能

### 支援新的日誌格式

1. **擴展解析器**
   ```python
   # 在 src/exchange_log_analyzer/core/parser.py 中添加
   def parse_custom_format(file_path: str) -> pd.DataFrame:
       # 實作自訂格式解析邏輯
       pass
   ```

2. **註冊新格式**
   ```python
   # 在 src/exchange_log_analyzer/core/config.py 中添加
   SUPPORTED_EXTENSIONS.append('.custom')
   ```

### 添加新的匯出格式

```python
# 在 src/exchange_log_analyzer/gui/main_window.py 中擴展
def export_excel(self):
    """匯出為 Excel 格式"""
    if self.filtered_data.empty:
        return

    filename, _ = QFileDialog.getSaveFileName(
        self, "匯出 Excel", "exchange_logs.xlsx",
        "Excel files (*.xlsx)"
    )

    if filename:
        self.filtered_data.to_excel(filename, index=False)
```

### 自訂 GUI 主題

```python
# 建立自訂樣式表
custom_style = """
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}
QTableWidget {
    background-color: #3c3c3c;
    alternate-background-color: #4a4a4a;
}
"""
app.setStyleSheet(custom_style)
```

## ❓ 常見問題

### Q: 為什麼無法載入某些 .log 檔案？
**A**: 請確認：
- 檔案格式符合 Exchange 訊息追蹤日誌標準
- 檔案沒有被其他程式鎖定
- 檔案編碼為 UTF-8 或 ASCII
- 檔案大小不超過 500MB（可在設定中調整）

### Q: SMB 連線功能無法使用？
**A**: 需要安裝額外套件：
```bash
pip install smbprotocol pysmb
```

### Q: 處理大型檔案時程式變慢？
**A**: 建議：
- 使用 64 位元 Python
- 增加系統記憶體
- 分批處理檔案
- 使用日期範圍篩選減少資料量

### Q: 匯出的 CSV 檔案中文顯示亂碼？
**A**: 程式已使用 UTF-8-BOM 編碼，如果仍有問題：
- 使用 Excel 開啟時選擇「資料」→「從文字」
- 選擇 UTF-8 編碼
- 或使用 LibreOffice Calc 開啟

### Q: 如何自訂日誌檔案路徑？
**A**: 修改設定檔或在程式中指定：
```python
from exchange_log_analyzer.core.config import Config
config = Config(log_folder="C:\\Custom\\Path\\To\\Logs")
```

## 🤝 貢獻指南

我們歡迎社群貢獻！請參考以下指南：

### 如何貢獻

1. **Fork 專案**到您的 GitHub 帳號
2. **建立功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交變更** (`git commit -m 'Add some AmazingFeature'`)
4. **推送分支** (`git push origin feature/AmazingFeature`)
5. **建立 Pull Request**

### 貢獻類型

- 🐛 **錯誤修復**: 修復已知問題
- ✨ **新功能**: 添加新的功能特性
- 📚 **文件改進**: 改善文件和範例
- 🎨 **介面優化**: 改善使用者體驗
- ⚡ **效能優化**: 提升程式效能
- 🧪 **測試增強**: 添加或改進測試

### 程式碼規範

- 遵循 [PEP 8](https://www.python.org/dev/peps/pep-0008/) 程式碼風格
- 使用有意義的變數和函式名稱
- 添加適當的註解和文件字串
- 撰寫對應的單元測試
- 確保所有測試通過

## 📄 授權條款

本專案採用 MIT 授權條款 - 詳見 [LICENSE](LICENSE) 檔案

```
MIT License

Copyright (c) 2025 Jacky Zou

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 📞 聯絡資訊

- **作者**: Jacky Zou 鄒嘉駿
- **Email**: [<EMAIL>](mailto:<EMAIL>)
- **組織**: TPV IT Global Infrastructure Team
- **GitHub**: [https://github.com/jackycj0830/ExchangeLogAnalyzer](https://github.com/jackycj0830/ExchangeLogAnalyzer)

### 支援管道

- 🐛 **問題回報**: [GitHub Issues](https://github.com/jackycj0830/ExchangeLogAnalyzer/issues)
- 💡 **功能建議**: [GitHub Discussions](https://github.com/jackycj0830/ExchangeLogAnalyzer/discussions)
- 📖 **文件**: [GitHub Wiki](https://github.com/jackycj0830/ExchangeLogAnalyzer/wiki)

---

## 🙏 致謝

感謝以下開源專案和貢獻者：

- [pandas](https://pandas.pydata.org/) - 強大的資料分析工具
- [PyQt5](https://www.riverbankcomputing.com/software/pyqt/) - 跨平台 GUI 框架
- [smbprotocol](https://github.com/jborean93/smbprotocol) - SMB 協定實作
- 所有提供回饋和建議的使用者

---

**⭐ 如果這個專案對您有幫助，請給我們一個 Star！**
