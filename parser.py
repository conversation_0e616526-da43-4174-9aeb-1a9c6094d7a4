import pandas as pd
import pytz

def parse_log(file_path, timezone: str = 'UTC'):
    try:
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            lines = [line.strip() for line in f if not line.startswith("#")]

        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            fields_line = next(line for line in f if line.startswith("#Fields:"))
            headers = fields_line.replace("#Fields: ", "").strip().split(",")

        data = [line.split(",", len(headers) - 1) for line in lines]
        df = pd.DataFrame(data, columns=headers)

        if 'date-time' in df.columns:
            df['date-time'] = pd.to_datetime(df['date-time'], errors='coerce', utc=True)
            if timezone and timezone != 'UTC':
                df['date-time'] = df['date-time'].dt.tz_convert(timezone)

        return df

    except Exception as e:
        print(f"\u274c \u7121\u6cd5\u89e3\u6790 LOG \u6a94\uff1a{file_path}\n\u932f\u8aa4\uff1a{e}")
        return pd.DataFrame()


def filter_logs(df, keyword=None, start_date=None, end_date=None):
    if keyword:
        mask = df.apply(lambda row: row.astype(str).str.contains(keyword, case=False, na=False).any(), axis=1)
        df = df[mask]

    if 'date-time' in df.columns:
        if start_date:
            df = df[df['date-time'] >= pd.to_datetime(start_date).tz_localize(df['date-time'].dt.tz)]
        if end_date:
            df = df[df['date-time'] <= pd.to_datetime(end_date).tz_localize(df['date-time'].dt.tz)]

    return df
