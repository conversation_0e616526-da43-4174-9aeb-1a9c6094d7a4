"""
Configuration management for Exchange Log Analyzer.

This module handles configuration settings and default paths for the application.
"""

import os
from typing import List, Optional
import logging

# Set up logging
logger = logging.getLogger(__name__)


class Config:
    """Configuration class for Exchange Log Analyzer."""
    
    # Default Exchange message tracking log path
    DEFAULT_LOG_FOLDER = r"C:\Program Files\Microsoft\Exchange Server\V15\TransportRoles\Logs\MessageTracking"
    
    # Supported file extensions
    SUPPORTED_EXTENSIONS = ['.log']
    
    # Default timezone
    DEFAULT_TIMEZONE = 'UTC'
    
    # GUI settings
    DEFAULT_WINDOW_WIDTH = 1200
    DEFAULT_WINDOW_HEIGHT = 800
    
    def __init__(self, log_folder: Optional[str] = None):
        """
        Initialize configuration.
        
        Args:
            log_folder (str, optional): Custom log folder path.
        """
        self.log_folder = log_folder or self.DEFAULT_LOG_FOLDER
        
    def find_log_files(self, folder_path: Optional[str] = None) -> List[str]:
        """
        Find all log files in the specified folder.
        
        Args:
            folder_path (str, optional): Path to search for log files.
                                       If None, uses the configured log folder.
        
        Returns:
            List[str]: List of full paths to log files.
        """
        search_path = folder_path or self.log_folder
        
        if not os.path.exists(search_path):
            logger.warning(f"Log folder does not exist: {search_path}")
            return []
        
        try:
            log_files = []
            for filename in os.listdir(search_path):
                file_path = os.path.join(search_path, filename)
                if (os.path.isfile(file_path) and 
                    any(filename.lower().endswith(ext) for ext in self.SUPPORTED_EXTENSIONS)):
                    log_files.append(file_path)
            
            logger.info(f"Found {len(log_files)} log files in {search_path}")
            return sorted(log_files)
            
        except Exception as e:
            logger.error(f"Error scanning log folder {search_path}: {e}")
            return []
    
    def validate_log_folder(self, folder_path: str) -> bool:
        """
        Validate if a folder path exists and is accessible.
        
        Args:
            folder_path (str): Path to validate.
            
        Returns:
            bool: True if valid, False otherwise.
        """
        try:
            return os.path.exists(folder_path) and os.path.isdir(folder_path)
        except Exception:
            return False


# Global configuration instance
config = Config()


def find_log_files(folder_path: Optional[str] = None) -> List[str]:
    """
    Convenience function to find log files.
    
    Args:
        folder_path (str, optional): Path to search for log files.
        
    Returns:
        List[str]: List of full paths to log files.
    """
    return config.find_log_files(folder_path)
