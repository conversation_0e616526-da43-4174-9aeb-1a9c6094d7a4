"""
Helper functions for Exchange Log Analyzer.

This module contains utility functions for common operations.
"""

import os
import logging
import datetime
from typing import Optional, Union
import pytz


def setup_logging(level: int = logging.INFO, log_file: Optional[str] = None) -> None:
    """
    Setup logging configuration for the application.
    
    Args:
        level (int): Logging level (e.g., logging.INFO, logging.DEBUG).
        log_file (str, optional): Path to log file. If None, logs to console only.
    """
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (if specified)
    if log_file:
        try:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(level)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            logging.warning(f"Failed to setup file logging: {e}")


def format_datetime(dt: Union[datetime.datetime, str], 
                   format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Format datetime object or string to a standardized format.
    
    Args:
        dt (datetime.datetime or str): Datetime to format.
        format_str (str): Format string for output.
        
    Returns:
        str: Formatted datetime string.
    """
    if isinstance(dt, str):
        try:
            dt = datetime.datetime.fromisoformat(dt.replace('Z', '+00:00'))
        except ValueError:
            return dt  # Return original if parsing fails
    
    if isinstance(dt, datetime.datetime):
        return dt.strftime(format_str)
    
    return str(dt)


def validate_file_path(file_path: str) -> bool:
    """
    Validate if a file path exists and is accessible.
    
    Args:
        file_path (str): Path to validate.
        
    Returns:
        bool: True if valid, False otherwise.
    """
    try:
        return os.path.exists(file_path) and os.path.isfile(file_path)
    except Exception:
        return False


def validate_directory_path(dir_path: str) -> bool:
    """
    Validate if a directory path exists and is accessible.
    
    Args:
        dir_path (str): Directory path to validate.
        
    Returns:
        bool: True if valid, False otherwise.
    """
    try:
        return os.path.exists(dir_path) and os.path.isdir(dir_path)
    except Exception:
        return False


def get_file_size_mb(file_path: str) -> float:
    """
    Get file size in megabytes.
    
    Args:
        file_path (str): Path to the file.
        
    Returns:
        float: File size in MB, or 0 if file doesn't exist.
    """
    try:
        size_bytes = os.path.getsize(file_path)
        return size_bytes / (1024 * 1024)
    except Exception:
        return 0.0


def convert_timezone(dt: datetime.datetime, target_tz: str) -> datetime.datetime:
    """
    Convert datetime to target timezone.
    
    Args:
        dt (datetime.datetime): Datetime to convert.
        target_tz (str): Target timezone name.
        
    Returns:
        datetime.datetime: Converted datetime.
    """
    try:
        if dt.tzinfo is None:
            # Assume UTC if no timezone info
            dt = pytz.UTC.localize(dt)
        
        target_timezone = pytz.timezone(target_tz)
        return dt.astimezone(target_timezone)
    except Exception as e:
        logging.warning(f"Failed to convert timezone: {e}")
        return dt


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename by removing invalid characters.
    
    Args:
        filename (str): Original filename.
        
    Returns:
        str: Sanitized filename.
    """
    # Remove invalid characters for Windows/Unix
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Ensure filename is not empty
    if not filename:
        filename = "untitled"
    
    return filename


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human-readable format.
    
    Args:
        size_bytes (int): Size in bytes.
        
    Returns:
        str: Formatted size string.
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"


def truncate_string(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    Truncate string to maximum length with suffix.
    
    Args:
        text (str): Text to truncate.
        max_length (int): Maximum length including suffix.
        suffix (str): Suffix to add when truncating.
        
    Returns:
        str: Truncated string.
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix
