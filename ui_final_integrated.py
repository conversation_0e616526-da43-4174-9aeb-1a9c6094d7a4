
# ✅ 完整整合版：自動翻譯表頭、自適應欄位、自動換行、匯出中文 CSV

from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit,
    QLabel, QDateEdit, QTimeEdit, QFileDialog, QTableWidget, QTableWidgetItem,
    QPlainTextEdit, QMessageBox, QMainWindow, QAction, QComboBox, QDialog, QTextEdit, QDialogButtonBox
)
from PyQt5.QtCore import QDate, QTime, QDateTime, Qt
from PyQt5.QtGui import QFont, QIcon
from parser import parse_log, filter_logs
from version import VERSION_INFO_TEXT
import pandas as pd
import os
import sys
import datetime
import re
import pytz


class LogAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Exchange 追蹤日誌分析工具 v1.0.4 (2025-05-17)")
        self.setWindowIcon(QIcon("svg_344955.ico"))
        self.setGeometry(100, 100, 1100, 700)

        self.df = pd.DataFrame()
        self.filtered_df = pd.DataFrame()
        self.current_page = 0
        self.rows_per_page = 100
        self.selected_timezone = 'UTC'

        menubar = self.menuBar()
        file_menu = menubar.addMenu("檔案")
        open_action = QAction("開啟 LOG 資料夾", self)
        open_action.triggered.connect(self.load_logs)
        file_menu.addAction(open_action)
        export_action = QAction("匯出查詢結果為 CSV", self)
        export_action.triggered.connect(self.export_csv)
        file_menu.addAction(export_action)
        exit_action = QAction("結束程式", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        help_menu = menubar.addMenu("說明")
        version_action = QAction("版本說明", self)
        version_action.triggered.connect(self.show_version_info)
        help_menu.addAction(version_action)

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout()

        date_layout = QHBoxLayout()
        self.start_date = QDateEdit(calendarPopup=True)
        self.start_date.setDisplayFormat("yyyy/MM/dd")
        self.start_date.setDate(QDate.currentDate().addDays(-7))
        self.start_time = QTimeEdit()
        self.start_time.setDisplayFormat("HH:mm")
        self.start_time.setTime(QTime(0, 0))
        self.end_date = QDateEdit(calendarPopup=True)
        self.end_date.setDisplayFormat("yyyy/MM/dd")
        self.end_date.setDate(QDate.currentDate())
        self.end_time = QTimeEdit()
        self.end_time.setDisplayFormat("HH:mm")
        self.end_time.setTime(QTime(23, 59))

        self.timezone_selector = QComboBox()
        self.timezone_selector.addItems(pytz.all_timezones)
        self.timezone_selector.setCurrentText('UTC')
        self.timezone_selector.currentTextChanged.connect(self.set_timezone)

        date_layout.addWidget(QLabel("起始日期:"))
        date_layout.addWidget(self.start_date)
        date_layout.addWidget(QLabel("起始時間:"))
        date_layout.addWidget(self.start_time)
        date_layout.addSpacing(20)
        date_layout.addWidget(QLabel("結束日期:"))
        date_layout.addWidget(self.end_date)
        date_layout.addWidget(QLabel("結束時間:"))
        date_layout.addWidget(self.end_time)
        date_layout.addSpacing(20)
        date_layout.addWidget(QLabel("顯示時區:"))
        date_layout.addWidget(self.timezone_selector)
        layout.addLayout(date_layout)

        self.keyword_input = QLineEdit()
        self.keyword_input.setPlaceholderText("輸入關鍵字（如寄件人、收件人、主旨、IP、EventId 等）")
        layout.addWidget(self.keyword_input)

        btn_layout = QHBoxLayout()
        load_btn = QPushButton("📂 選擇資料夾並載入 LOG")
        load_btn.clicked.connect(self.load_logs)
        btn_layout.addWidget(load_btn)
        filter_btn = QPushButton("🔍 執行查詢")
        filter_btn.clicked.connect(self.apply_filter)
        btn_layout.addWidget(filter_btn)
        export_btn = QPushButton("📤 匯出查詢結果為 CSV")
        export_btn.clicked.connect(self.export_csv)
        btn_layout.addWidget(export_btn)
        layout.addLayout(btn_layout)

        self.info_box = QPlainTextEdit()
        self.info_box.setReadOnly(True)
        self.info_box.setLineWrapMode(QPlainTextEdit.NoWrap)
        font = QFont("Courier New")
        font.setPointSize(10)
        self.info_box.setFont(font)
        self.info_box.setPlainText(VERSION_INFO_TEXT)
        layout.addWidget(self.info_box)

        pagination_layout = QHBoxLayout()
        self.page_label = QLabel("頁數：0 / 0")
        self.prev_button = QPushButton("⬅ 上一頁")
        self.next_button = QPushButton("➡ 下一頁")
        self.page_size_selector = QComboBox()
        self.page_size_selector.addItems(["100", "200", "300", "500", "1000", "2000"])
        self.page_size_selector.setCurrentText("100")
        self.page_size_selector.currentIndexChanged.connect(self.change_page_size)
        self.prev_button.clicked.connect(self.go_to_prev_page)
        self.next_button.clicked.connect(self.go_to_next_page)
        pagination_layout.addWidget(self.prev_button)
        pagination_layout.addWidget(self.next_button)
        pagination_layout.addWidget(QLabel("每頁筆數:"))
        pagination_layout.addWidget(self.page_size_selector)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.page_label)
        layout.addLayout(pagination_layout)

        self.table = QTableWidget()
        self.table.hide()
        layout.addWidget(self.table)
        central_widget.setLayout(layout)

    def set_timezone(self, tz):
        self.selected_timezone = tz

    def extract_datetime_from_filename(self, filename):
        try:
            match = re.search(r"MSGTRK(\d{10})", filename.upper())
            if match:
                datetime_str = match.group(1)
                return datetime.datetime.strptime(datetime_str, "%Y%m%d%H")
            return None
        except Exception:
            return None

    def load_logs(self):
        folder_path = QFileDialog.getExistingDirectory(self, "選擇含 Exchange LOG 檔的資料夾")
        if not folder_path:
            return
        all_files = [f for f in os.listdir(folder_path)
                     if f.lower().endswith(".log") and os.path.isfile(os.path.join(folder_path, f))]
        start_dt = QDateTime(self.start_date.date(), self.start_time.time()).toPyDateTime()
        end_dt = QDateTime(self.end_date.date(), self.end_time.time()).toPyDateTime()
        matching_files = []
        for filename in all_files:
            file_dt = self.extract_datetime_from_filename(filename)
            if file_dt and start_dt <= file_dt <= end_dt:
                matching_files.append(os.path.join(folder_path, filename))
        if not matching_files:
            QMessageBox.warning(self, "⚠️ 找不到符合時間的 LOG", "沒有檔案符合選定的日期與時間區間。")
            return
        all_dfs = [parse_log(f, timezone=self.selected_timezone) for f in matching_files]
        self.df = pd.concat(all_dfs, ignore_index=True)
        self.apply_filter()

    def apply_filter(self):
        if self.df.empty:
            QMessageBox.information(self, "請先載入 LOG", "請先載入 Exchange LOG 檔案。")
            return
        keyword = self.keyword_input.text()
        start_dt = QDateTime(self.start_date.date(), self.start_time.time()).toPyDateTime()
        end_dt = QDateTime(self.end_date.date(), self.end_time.time()).toPyDateTime()
        self.filtered_df = filter_logs(self.df, keyword, start_dt, end_dt)
        self.current_page = 0
        self.update_table_page()

    
def update_table_page(self):
    from PyQt5.QtGui import QColor

    translation_map = {
        "date-time": "日期時間", "client-ip": "用戶端IP", "client-hostname": "用戶端主機名稱",
        "server-ip": "伺服器IP", "server-hostname": "伺服器主機名稱", "source-context": "來源上下文",
        "connector-id": "連接器ID", "source": "來源", "event-id": "事件ID",
        "internal-message-id": "內部訊息ID", "message-id": "訊息ID", "network-message-id": "網路訊息ID",
        "recipient-address": "收件人地址", "recipient-status": "收件人狀態", "total-bytes": "總位元組數",
        "recipient-count": "收件人數量", "related-recipient-address": "相關收件人地址", "reference": "參考",
        "message-subject": "主旨", "sender-address": "寄件人地址", "return-path": "退信路徑",
        "message-info": "訊息資訊", "directionality": "方向性", "tenant-id": "租戶ID",
        "original-client-ip": "原始用戶端IP", "original-server-ip": "原始伺服器IP", "custom-data": "自訂資料",
        "transport-traffic-type": "傳輸流量類型", "log-id": "日誌ID", "schema-version": "結構版本"
    }

    total_rows = len(self.filtered_df)
    rows_per_page = self.rows_per_page
    total_pages = (total_rows - 1) // rows_per_page + 1 if total_rows else 1
    self.page_label.setText(f"頁數：{self.current_page + 1} / {total_pages}　　總筆數：{total_rows}")
    start = self.current_page * rows_per_page
    end = min(start + rows_per_page, total_rows)
    df = self.filtered_df.iloc[start:end]

    self.table.setRowCount(len(df))
    self.table.setColumnCount(len(df.columns) + 1)  # 多一欄顯示流水號
    translated_headers = ['#'] + [translation_map.get(col, col) for col in df.columns]
    self.table.setHorizontalHeaderLabels(translated_headers)

    keyword = self.keyword_input.text().strip().lower()

    for i in range(len(df)):
        serial_number = start + i + 1
        serial_item = QTableWidgetItem(f"{serial_number:03d}")
        serial_item.setFlags(serial_item.flags() ^ Qt.ItemIsEditable)
        serial_item.setTextAlignment(Qt.AlignCenter)
        self.table.setItem(i, 0, serial_item)

        for j in range(len(df.columns)):
            content = str(df.iat[i, j])
            item = QTableWidgetItem(content)
            item.setFlags(item.flags() ^ Qt.ItemIsEditable)
            item.setTextAlignment(Qt.AlignLeft | Qt.AlignTop)
            item.setFont(QFont("Microsoft JhengHei", 10))
            if keyword and keyword in content.lower():
                item.setBackground(QColor("yellow"))
            self.table.setItem(i, j + 1, item)

    self.table.resizeColumnsToContents()
    self.table.resizeRowsToContents()
    self.table.show()
    self.info_box.hide()

    def go_to_prev_page(self):
        if self.current_page > 0:
            self.current_page -= 1
            self.update_table_page()

    def go_to_next_page(self):
        max_page = (len(self.filtered_df) - 1) // self.rows_per_page
        if self.current_page < max_page:
            self.current_page += 1
            self.update_table_page()

    def change_page_size(self):
        self.rows_per_page = int(self.page_size_selector.currentText())
        self.current_page = 0
        self.update_table_page()

    def export_csv(self):
        if self.filtered_df.empty:
            QMessageBox.information(self, "⚠️ 無資料", "請先查詢再匯出。")
            return

        translation_map = {
            "date-time": "日期時間", "client-ip": "用戶端IP", "client-hostname": "用戶端主機名稱",
            "server-ip": "伺服器IP", "server-hostname": "伺服器主機名稱", "source-context": "來源上下文",
            "connector-id": "連接器ID", "source": "來源", "event-id": "事件ID",
            "internal-message-id": "內部訊息ID", "message-id": "訊息ID", "network-message-id": "網路訊息ID",
            "recipient-address": "收件人地址", "recipient-status": "收件人狀態", "total-bytes": "總位元組數",
            "recipient-count": "收件人數量", "related-recipient-address": "相關收件人地址", "reference": "參考",
            "message-subject": "主旨", "sender-address": "寄件人地址", "return-path": "退信路徑",
            "message-info": "訊息資訊", "directionality": "方向性", "tenant-id": "租戶ID",
            "original-client-ip": "原始用戶端IP", "original-server-ip": "原始伺服器IP", "custom-data": "自訂資料",
            "transport-traffic-type": "傳輸流量類型", "log-id": "日誌ID", "schema-version": "結構版本"
        }

        translated_df = self.filtered_df.rename(columns=translation_map)
        path, _ = QFileDialog.getSaveFileName(self, "儲存 CSV 檔案", filter="CSV Files (*.csv)")
        if path:
            translated_df.to_csv(path, index=False, encoding='utf-8-sig')
            QMessageBox.information(self, "✅ 匯出成功", f"已匯出：\n{path}")

    def show_version_info(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("版本說明")
        dialog.setMinimumWidth(1000)
        layout = QVBoxLayout()
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setText(VERSION_INFO_TEXT)
        text_edit.setFont(QFont("Courier New", 10))
        layout.addWidget(text_edit)
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(dialog.accept)
        layout.addWidget(button_box)
        dialog.setLayout(layout)
        dialog.exec_()
