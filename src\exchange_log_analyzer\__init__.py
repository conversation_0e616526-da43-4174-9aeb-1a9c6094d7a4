"""
Exchange Log Analyzer - A tool for analyzing Microsoft Exchange message tracking logs.

This package provides functionality to parse, filter, and analyze Exchange Server
message tracking logs with a user-friendly GUI interface.

Author: <PERSON><PERSON>: <EMAIL>
Version: 1.0.9
"""

__version__ = "1.0.9"
__author__ = "<PERSON><PERSON>"
__email__ = "<EMAIL>"

from .core.parser import LogParser
from .core.config import Config
from .gui.main_window import LogAnalyzer

__all__ = ['LogParser', 'Config', 'LogAnalyzer']
