"""
Main entry point for Exchange Log Analyzer.

This module provides the main entry point for running the application.
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# Add the src directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from exchange_log_analyzer.gui.main_window import LogAnalyzer
from exchange_log_analyzer.utils.helpers import setup_logging
from exchange_log_analyzer.utils.constants import DEFAULT_LOG_LEVEL


def main():
    """Main entry point for the application."""
    # Setup logging
    setup_logging(getattr(logging, DEFAULT_LOG_LEVEL))
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Exchange Log Analyzer")
    app.setApplicationVersion("1.0.9")
    app.setOrganizationName("TPV IT Global Infrastructure Team")
    app.setOrganizationDomain("tpv-tech.com")
    
    # Enable high DPI scaling
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # Create and show main window
    try:
        window = LogAnalyzer()
        window.show()
        
        # Start event loop
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
