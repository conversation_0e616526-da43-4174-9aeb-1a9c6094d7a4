"""
Constants for Exchange Log Analyzer.

This module contains application-wide constants and configuration values.
"""

# Supported timezones
SUPPORTED_TIMEZONES = [
    'UTC',
    'Asia/Taipei',
    'Asia/Shanghai',
    'Asia/Tokyo',
    'Asia/Seoul',
    'US/Eastern',
    'US/Central',
    'US/Mountain',
    'US/Pacific',
    'Europe/London',
    'Europe/Paris',
    'Europe/Berlin',
    'Australia/Sydney',
    'Australia/Melbourne'
]

# Date and time formats
DEFAULT_DATE_FORMAT = "%Y-%m-%d"
DEFAULT_TIME_FORMAT = "%H:%M:%S"
DEFAULT_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
ISO_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S"

# File extensions
SUPPORTED_LOG_EXTENSIONS = ['.log', '.txt']
SUPPORTED_EXPORT_EXTENSIONS = ['.csv', '.xlsx', '.json']

# Application settings
APP_NAME = "Exchange Log Analyzer"
APP_VERSION = "1.0.9"
APP_AUTHOR = "Jacky Z<PERSON>"
APP_EMAIL = "<EMAIL>"

# GUI settings
DEFAULT_WINDOW_WIDTH = 1200
DEFAULT_WINDOW_HEIGHT = 800
MIN_WINDOW_WIDTH = 800
MIN_WINDOW_HEIGHT = 600

# Table settings
MAX_DISPLAY_ROWS = 10000  # Maximum rows to display in table for performance
DEFAULT_ROW_HEIGHT = 25
DEFAULT_COLUMN_WIDTH = 150

# Search settings
MAX_SEARCH_RESULTS = 50000  # Maximum search results to prevent memory issues
SEARCH_TIMEOUT_SECONDS = 30

# File processing settings
MAX_FILE_SIZE_MB = 500  # Maximum file size to process
CHUNK_SIZE_ROWS = 1000  # Number of rows to process at a time
MAX_CONCURRENT_FILES = 5  # Maximum files to process concurrently

# SMB settings
DEFAULT_SMB_SHARE = 'LogsMessageTracking'
SMB_TIMEOUT_SECONDS = 30
MAX_SMB_RETRIES = 3

# Logging settings
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
DEFAULT_LOG_LEVEL = 'INFO'

# Exchange log field mappings (for translation)
FIELD_TRANSLATIONS = {
    'date-time': '日期時間',
    'client-ip': '客戶端IP',
    'client-hostname': '客戶端主機名',
    'server-ip': '伺服器IP',
    'server-hostname': '伺服器主機名',
    'source-context': '來源內容',
    'connector-id': '連接器ID',
    'source': '來源',
    'event-id': '事件ID',
    'internal-message-id': '內部訊息ID',
    'message-id': '訊息ID',
    'network-message-id': '網路訊息ID',
    'recipient-address': '收件者地址',
    'recipient-status': '收件者狀態',
    'total-bytes': '總位元組',
    'recipient-count': '收件者數量',
    'related-recipient-address': '相關收件者地址',
    'reference': '參考',
    'message-subject': '訊息主旨',
    'sender-address': '寄件者地址',
    'return-path': '回傳路徑',
    'message-info': '訊息資訊',
    'directionality': '方向性',
    'tenant-id': '租戶ID',
    'original-client-ip': '原始客戶端IP',
    'original-server-ip': '原始伺服器IP',
    'custom-data': '自訂資料'
}

# Common Exchange event IDs and their descriptions
EVENT_ID_DESCRIPTIONS = {
    'RECEIVE': '接收',
    'SEND': '傳送',
    'FAIL': '失敗',
    'DSN': '傳遞狀態通知',
    'DEFER': '延遲',
    'DELIVER': '傳遞',
    'AGENTINFO': '代理程式資訊',
    'TRANSFER': '轉送',
    'RESOLVE': '解析',
    'EXPAND': '展開',
    'REDIRECT': '重新導向',
    'RESUBMIT': '重新提交',
    'DUPLICATEDELIVER': '重複傳遞',
    'POISONMESSAGE': '有害訊息',
    'BADMAIL': '錯誤郵件'
}

# File size limits
MAX_LOG_FILE_SIZE_BYTES = 500 * 1024 * 1024  # 500 MB
WARN_LOG_FILE_SIZE_BYTES = 100 * 1024 * 1024  # 100 MB

# Performance settings
PANDAS_DISPLAY_MAX_ROWS = 100
PANDAS_DISPLAY_MAX_COLUMNS = 20
