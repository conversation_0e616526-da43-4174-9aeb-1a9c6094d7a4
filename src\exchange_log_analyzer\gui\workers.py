"""
Background worker threads for Exchange Log Analyzer.

This module contains worker threads for performing long-running operations
without blocking the GUI.
"""

import pandas as pd
from PyQt5.QtCore import QThread, pyqtSignal
from typing import List
import logging

logger = logging.getLogger(__name__)


class LogProcessingWorker(QThread):
    """Worker thread for processing log files in the background."""
    
    # Signals
    finished = pyqtSignal(pd.DataFrame)
    error = pyqtSignal(str)
    progress = pyqtSignal(int, str)
    
    def __init__(self, log_files: List[str], parser, parent=None):
        """
        Initialize the worker.
        
        Args:
            log_files (List[str]): List of log file paths to process.
            parser: LogParser instance for parsing files.
            parent: Parent QObject.
        """
        super().__init__(parent)
        self.log_files = log_files
        self.parser = parser
        self._is_cancelled = False
        
    def run(self):
        """Run the log processing in the background."""
        try:
            all_data = []
            total_files = len(self.log_files)
            
            for i, file_path in enumerate(self.log_files):
                if self._is_cancelled:
                    return
                    
                # Emit progress
                self.progress.emit(
                    int((i / total_files) * 100),
                    f"處理檔案 {i+1}/{total_files}: {file_path}"
                )
                
                # Parse the file
                try:
                    df = self.parser.parse_file(file_path)
                    if not df.empty:
                        all_data.append(df)
                        logger.info(f"Successfully processed {file_path}")
                    else:
                        logger.warning(f"No data found in {file_path}")
                        
                except Exception as e:
                    logger.error(f"Error processing {file_path}: {e}")
                    continue
                    
            # Combine all data
            if all_data:
                combined_data = pd.concat(all_data, ignore_index=True)
                # Sort by date-time if available
                if 'date-time' in combined_data.columns:
                    combined_data = combined_data.sort_values('date-time')
                    
                self.finished.emit(combined_data)
            else:
                self.error.emit("無法從任何檔案中讀取資料")
                
        except Exception as e:
            logger.error(f"Error in log processing worker: {e}")
            self.error.emit(f"處理過程中發生錯誤: {str(e)}")
            
    def cancel(self):
        """Cancel the processing."""
        self._is_cancelled = True


class SearchWorker(QThread):
    """Worker thread for searching/filtering log data."""
    
    # Signals
    finished = pyqtSignal(pd.DataFrame)
    error = pyqtSignal(str)
    progress = pyqtSignal(int, str)
    
    def __init__(self, data: pd.DataFrame, keyword: str = None, 
                 start_date=None, end_date=None, parent=None):
        """
        Initialize the search worker.
        
        Args:
            data (pd.DataFrame): Data to search/filter.
            keyword (str): Keyword to search for.
            start_date: Start date for filtering.
            end_date: End date for filtering.
            parent: Parent QObject.
        """
        super().__init__(parent)
        self.data = data
        self.keyword = keyword
        self.start_date = start_date
        self.end_date = end_date
        self._is_cancelled = False
        
    def run(self):
        """Run the search/filtering in the background."""
        try:
            from ..core.parser import filter_logs
            
            self.progress.emit(50, "正在篩選資料...")
            
            if self._is_cancelled:
                return
                
            # Perform filtering
            filtered_data = filter_logs(
                self.data, self.keyword, self.start_date, self.end_date
            )
            
            if self._is_cancelled:
                return
                
            self.progress.emit(100, "篩選完成")
            self.finished.emit(filtered_data)
            
        except Exception as e:
            logger.error(f"Error in search worker: {e}")
            self.error.emit(f"搜尋過程中發生錯誤: {str(e)}")
            
    def cancel(self):
        """Cancel the search."""
        self._is_cancelled = True
