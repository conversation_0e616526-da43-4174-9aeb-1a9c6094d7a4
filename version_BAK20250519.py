# version.py

VERSION_INFO_TEXT = """
+-----------------------------------------------------------------------------------------------------+
| 📊 Exchange 追蹤日誌分析工具 Version Information                                                       |
+-----------------------------------------------------------------------------------------------------+
| Original Author:                                                                                   |
|   Exchange 追蹤日誌分析工具 v1.0.6 (2025-05-19)                                                     |
|   Author: <PERSON><PERSON> 鄒嘉駿 (TPV Global Infrastructure Team)                                        |
|   Email: <EMAIL>                                                                    |
+-----------------------------------------------------------------------------------------------------+
| Disclaimer:                                                                                        |
|   This script is provided as is without any warranty. Use it at your own risk.                     |
|   The author is not responsible for any damages or losses caused by the use of this tool.          |
+-----------------------------------------------------------------------------------------------------+
| Exchange 追蹤日誌分析工具 (By Jacky Zou, editor)  v.1.0.6_20250519                                 |
| v.1.0.1_20250515: 改進版本                                                                      |
| 功能需求:                                                                                          |
| 1. 改進讀取效能與增加分頁功能                                                                      |
| v.1.0.0_20250514: 初階測試版本                                                                      |
| 功能需求:                                                                                          |
| 1. 查看所有 Exchange 日誌 Log 文件                                                                  |
| 2. 顯示版本信息                                                                                     |
+-----------------------------------------------------------------------------------------------------+
| 功能                          | 說明                                                                |
| ------------------------------| --------------------------------------------------------------------|
| 🗓 日期+🕒時間選擇（起始／結束）| 使用 `QDateTimeEdit` 顯示 `yyyy/MM/dd HH:mm`，可鍵入／調整時間       |
| 📤 載入 Exchange LOG 資料夾   | 支援選擇資料夾、自動讀取 `.log` 檔                                  |
| 🔍 關鍵字查詢                 | 可依日期時間＋關鍵字進行條件篩選                                    |
| 🧾 顯示外部版權檔              | 自動讀取 `version.py` 內容顯示在主畫面                              |
| 📤 匯出為 CSV                 | 可將查詢結果匯出                                                    |
+-----------------------------------------------------------------------------------------------------+
"""
